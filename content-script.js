// Content script for mouse tracking
// This script is injected into web pages to track mouse movements and clicks

(function() {
    'use strict';
    
    // Prevent multiple injections
    if (window.mouseTrackerInjected) {
        console.log('Mouse tracker already injected');
        return;
    }
    
    console.log('Injecting mouse tracker content script');
    window.mouseTrackerInjected = true;
    
    let isTracking = false;
    let mouseMoveHandler = null;
    let clickHandler = null;
    
    // Throttle mousemove events to avoid overwhelming the background script
    let lastMouseMove = 0;
    const mouseMoveThrottle = 50; // 50ms throttle
    
    function sendMouseData(action, coords) {
        if (!isTracking) return;
        
        console.log('Content script sending mouse data:', action, coords);
        try {
            chrome.runtime.sendMessage({
                origin: 'content',
                content: { action, coords }
            });
        } catch (error) {
            console.error('Error sending mouse data from content script:', error);
        }
    }
    
    function startTracking() {
        if (isTracking) {
            console.log('Mouse tracking already active');
            return;
        }
        
        console.log('Starting mouse tracking in content script');
        isTracking = true;
        
        // Create throttled mousemove handler
        mouseMoveHandler = (e) => {
            const now = Date.now();
            if (now - lastMouseMove > mouseMoveThrottle) {
                sendMouseData('move', { x: e.clientX, y: e.clientY });
                lastMouseMove = now;
            }
        };
        
        // Create click handler
        clickHandler = (e) => {
            sendMouseData('click', { x: e.clientX, y: e.clientY });
        };
        
        // Add event listeners
        document.addEventListener('mousemove', mouseMoveHandler, { passive: true });
        document.addEventListener('click', clickHandler, { passive: true });
        
        console.log('Mouse tracking event listeners added');
    }
    
    function stopTracking() {
        if (!isTracking) {
            console.log('Mouse tracking not active');
            return;
        }
        
        console.log('Stopping mouse tracking in content script');
        isTracking = false;
        
        // Remove event listeners
        if (mouseMoveHandler) {
            document.removeEventListener('mousemove', mouseMoveHandler);
            mouseMoveHandler = null;
        }
        
        if (clickHandler) {
            document.removeEventListener('click', clickHandler);
            clickHandler = null;
        }
        
        console.log('Mouse tracking event listeners removed');
    }
    
    // Listen for messages from the extension
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
        console.log('Content script received message:', message);
        
        if (message.action === 'startMouseTracking') {
            startTracking();
            sendResponse({ success: true });
        } else if (message.action === 'stopMouseTracking') {
            stopTracking();
            sendResponse({ success: true });
        }
        
        return true; // Keep message channel open for async response
    });
    
    // Auto-start tracking if the page is already loaded and tracking was requested
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Content script DOM loaded, ready for tracking');
        });
    } else {
        console.log('Content script loaded, DOM already ready');
    }
    
    console.log('Mouse tracker content script initialized');
})();
